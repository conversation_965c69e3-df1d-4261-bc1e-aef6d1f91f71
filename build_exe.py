#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script to create Windows executable for PPT to PNG Converter

This script uses PyInstaller to package the PPT converter into a standalone executable.
"""

import os
import sys
import subprocess
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to install PyInstaller: {e}")
            return False

def create_spec_files():
    """Create PyInstaller spec files for both console and GUI versions"""
    
    # Console version spec
    console_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ppt_to_png_converter.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'comtypes.gen',
        'comtypes.client',
        'PIL',
        'PIL.Image',
        'PIL.ImageChops',
        'numpy',
        'pathlib',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PPT_to_PNG_Console',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    # GUI version spec
    gui_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ppt_to_png_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'comtypes.gen',
        'comtypes.client',
        'PIL',
        'PIL.Image',
        'PIL.ImageChops',
        'numpy',
        'pathlib',
        'logging',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'threading',
        'queue'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PPT_to_PNG_GUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    # Write console version spec
    with open('ppt_console.spec', 'w', encoding='utf-8') as f:
        f.write(console_spec)
    print("Created console spec file: ppt_console.spec")
    
    # Write GUI version spec
    with open('ppt_gui.spec', 'w', encoding='utf-8') as f:
        f.write(gui_spec)
    print("Created GUI spec file: ppt_gui.spec")

def build_executables():
    """Build both console and GUI executables using PyInstaller"""
    print("Building executables...")
    
    success_count = 0
    
    # Build console version
    print("\n🔨 Building console version...")
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "ppt_console.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Console version built successfully!")
            console_path = Path("dist/PPT_to_PNG_Console.exe")
            if console_path.exists():
                size_mb = console_path.stat().st_size / (1024 * 1024)
                print(f"📊 Console executable size: {size_mb:.1f} MB")
                success_count += 1
        else:
            print("❌ Console build failed!")
            print("STDERR:", result.stderr[:500])
            
    except Exception as e:
        print(f"❌ Error building console version: {e}")
    
    # Build GUI version
    print("\n🔨 Building GUI version...")
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "ppt_gui.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ GUI version built successfully!")
            gui_path = Path("dist/PPT_to_PNG_GUI.exe")
            if gui_path.exists():
                size_mb = gui_path.stat().st_size / (1024 * 1024)
                print(f"📊 GUI executable size: {size_mb:.1f} MB")
                success_count += 1
        else:
            print("❌ GUI build failed!")
            print("STDERR:", result.stderr[:500])
            
    except Exception as e:
        print(f"❌ Error building GUI version: {e}")
    
    return success_count > 0

def create_batch_files():
    """Create batch files for easy execution"""
    
    # Console version batch file
    console_batch = '''@echo off
echo PPT to PNG Converter - Console Version
echo ========================================
echo.
echo Usage Examples:
echo   PPT_to_PNG_Console.exe "input_folder"
echo   PPT_to_PNG_Console.exe "input_folder" "output_folder" 600 100 10 true
echo.
echo Parameters:
echo   1. input_folder: Path to folder with PPT files (required)
echo   2. output_folder: Output folder (optional)
echo   3. dpi: Image resolution (optional, default: 300)
echo   4. border_threshold: Min border size to remove (optional, default: 50)
echo   5. min_border: Min border to keep (optional, default: 10)
echo   6. single_folder: Save all in one folder - true/false (optional, default: false)
echo.
pause
'''
    
    # GUI version batch file
    gui_batch = '''@echo off
echo Starting PPT to PNG Converter GUI...
start PPT_to_PNG_GUI.exe
'''
    
    # Create dist directory if it doesn't exist
    Path('dist').mkdir(exist_ok=True)
    
    with open('dist/console_help.bat', 'w', encoding='utf-8') as f:
        f.write(console_batch)
    print("Created console help file: dist/console_help.bat")
    
    with open('dist/start_gui.bat', 'w', encoding='utf-8') as f:
        f.write(gui_batch)
    print("Created GUI launcher: dist/start_gui.bat")

def main():
    """Main build process"""
    print("🚀 Building PPT to PNG Converter Executable")
    print("=" * 50)
    
    # Check if main script exists
    if not Path("ppt_to_png_converter.py").exists():
        print("❌ ppt_to_png_converter.py not found in current directory")
        return False
    
    # Install PyInstaller
    if not install_pyinstaller():
        return False
    
    # Create spec files
    create_spec_files()
    
    # Build executables
    if not build_executables():
        return False
    
    # Create batch files
    create_batch_files()
    
    print("\n✅ Build completed successfully!")
    print("\n📦 Distribution files:")
    print("  - dist/PPT_to_PNG_Console.exe (command-line version)")
    print("  - dist/PPT_to_PNG_GUI.exe (graphical interface version)")
    print("  - dist/console_help.bat (console usage help)")
    print("  - dist/start_gui.bat (GUI launcher)")
    print("\n🎯 To use on other Windows machines:")
    print("  1. Copy the entire 'dist' folder to the target machine")
    print("  2. Double-click PPT_to_PNG_GUI.exe for easy GUI interface")
    print("  3. Or use PPT_to_PNG_Console.exe from command line")
    print("  4. Run console_help.bat for command-line usage examples")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
    else:
        input("\nPress Enter to exit...")
