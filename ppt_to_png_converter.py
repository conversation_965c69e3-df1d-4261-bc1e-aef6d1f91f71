#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PowerPoint to PNG Converter with White Border Removal

This script converts all PowerPoint files in a specified folder to high-resolution PNG images
and removes excessive white borders from the resulting images while preserving original aspect ratio.

Requirements:
- python-pptx
- Pillow (PIL)
- comtypes (for Windows PowerPoint automation)
"""

import os
import sys
from pathlib import Path
import logging
from PIL import Image, ImageChops
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PPTToPNGConverter:
    def __init__(self, input_folder, output_folder=None, dpi=300, border_threshold=50, 
                 min_border=10, single_folder=False):
        """
        Initialize the converter
        
        Args:
            input_folder (str): Path to folder containing PPT files
            output_folder (str): Path to output folder (default: input_folder/output)
            dpi (int): DPI for PNG output (default: 300 for high resolution)
            border_threshold (int): Minimum border size to remove (pixels)
            min_border (int): Minimum white border to keep after cropping (pixels)
            single_folder (bool): If True, save all PNGs in one folder with PPTName_slideN.png naming
        """
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder) if output_folder else self.input_folder / "output"
        self.dpi = dpi
        self.border_threshold = border_threshold
        self.min_border = min_border
        self.single_folder = single_folder
        
        # Create output folder if it doesn't exist
        self.output_folder.mkdir(exist_ok=True)
        
        # Supported PowerPoint file extensions
        self.ppt_extensions = {'.ppt', '.pptx', '.pptm'}
    
    def find_ppt_files(self):
        """Find all PowerPoint files in the input folder"""
        ppt_files = []
        for ext in self.ppt_extensions:
            ppt_files.extend(self.input_folder.glob(f"*{ext}"))
        return ppt_files
    
    def get_slide_dimensions(self, presentation):
        """Get the original slide dimensions from PowerPoint presentation"""
        try:
            # Get slide dimensions in points (1 point = 1/72 inch)
            slide_width_points = presentation.PageSetup.SlideWidth
            slide_height_points = presentation.PageSetup.SlideHeight
            
            # Convert points to pixels at the specified DPI
            # 1 point = 1/72 inch, so pixels = points * DPI / 72
            slide_width_pixels = int(slide_width_points * self.dpi / 72)
            slide_height_pixels = int(slide_height_points * self.dpi / 72)
            
            logger.info(f"Original slide dimensions: {slide_width_points:.1f}x{slide_height_points:.1f} points")
            logger.info(f"Target PNG dimensions: {slide_width_pixels}x{slide_height_pixels} pixels at {self.dpi} DPI")
            
            return slide_width_pixels, slide_height_pixels
            
        except Exception as e:
            logger.warning(f"Could not get slide dimensions: {e}")
            # Fallback to standard 16:9 aspect ratio
            slide_width_pixels = int(16 * self.dpi / 2.54)  # ~16 cm width
            slide_height_pixels = int(9 * self.dpi / 2.54)   # ~9 cm height
            return slide_width_pixels, slide_height_pixels
    
    def convert_ppt_to_png_windows(self, ppt_file):
        """
        Convert PPT to PNG using Windows PowerPoint COM automation
        This method works best on Windows with PowerPoint installed
        """
        powerpoint = None
        presentation = None
        
        try:
            import comtypes.client
            import time
            
            # Start PowerPoint application with error handling
            logger.info(f"Starting PowerPoint for {ppt_file.name}...")
            powerpoint = comtypes.client.CreateObject("Powerpoint.Application")
            powerpoint.Visible = 1  # PowerPoint must be visible (security restriction)
            
            # Open presentation with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    presentation = powerpoint.Presentations.Open(str(ppt_file.absolute()))
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Attempt {attempt + 1} failed to open {ppt_file.name}: {e}. Retrying...")
                        time.sleep(2)
                    else:
                        raise e
            
            # Get original slide dimensions
            slide_width, slide_height = self.get_slide_dimensions(presentation)
            
            # Determine output folder structure
            if self.single_folder:
                # All PNGs in one folder
                ppt_output_folder = self.output_folder
            else:
                # Create output subfolder for this presentation
                ppt_output_folder = self.output_folder / ppt_file.stem
                ppt_output_folder.mkdir(exist_ok=True)
            
            # Export each slide as PNG with original aspect ratio
            total_slides = presentation.Slides.Count
            logger.info(f"Converting {total_slides} slides from {ppt_file.name}...")
            
            for i in range(1, total_slides + 1):
                try:
                    slide = presentation.Slides(i)
                    
                    # Generate filename based on output mode
                    if self.single_folder:
                        filename = f"{ppt_file.stem}_slide{i}.png"
                    else:
                        filename = f"slide_{i:03d}.png"
                    
                    output_path = ppt_output_folder / filename
                    
                    # Export with original dimensions to preserve aspect ratio
                    slide.Export(str(output_path), "PNG", slide_width, slide_height)
                    logger.info(f"Exported slide {i}/{total_slides} from {ppt_file.name} as {filename} ({slide_width}x{slide_height})")
                    
                    # Small delay to prevent COM overload
                    time.sleep(0.5)
                    
                except Exception as slide_error:
                    logger.error(f"Failed to export slide {i} from {ppt_file.name}: {slide_error}")
                    continue
            
            # Close presentation safely
            if presentation:
                try:
                    presentation.Close()
                    logger.info(f"Closed presentation {ppt_file.name}")
                except Exception as e:
                    logger.warning(f"Error closing presentation: {e}")
            
            # Close PowerPoint safely
            if powerpoint:
                try:
                    powerpoint.Quit()
                    logger.info("PowerPoint application closed")
                except Exception as e:
                    logger.warning(f"Error closing PowerPoint: {e}")
            
            # Wait a bit before processing next file
            time.sleep(1)
            
            return ppt_output_folder
            
        except ImportError:
            logger.error("comtypes not available. Please install: pip install comtypes")
            return None
        except Exception as e:
            logger.error(f"Error converting {ppt_file.name}: {str(e)}")
            # Comprehensive cleanup
            try:
                if presentation:
                    presentation.Close()
            except:
                pass
            try:
                if powerpoint:
                    powerpoint.Quit()
            except:
                pass
            return None
    
    def convert_ppt_to_png_libreoffice(self, ppt_file):
        """
        Convert PPT to PNG using LibreOffice headless mode
        This is a cross-platform alternative
        """
        try:
            import subprocess
            
            # Create output subfolder for this presentation
            ppt_output_folder = self.output_folder / ppt_file.stem
            ppt_output_folder.mkdir(exist_ok=True)
            
            # Use LibreOffice to convert to PDF first, then to PNG
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to", "png",
                "--outdir", str(ppt_output_folder),
                str(ppt_file.absolute())
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Converted {ppt_file.name} using LibreOffice")
                return ppt_output_folder
            else:
                logger.error(f"LibreOffice conversion failed: {result.stderr}")
                return None
                
        except FileNotFoundError:
            logger.error("LibreOffice not found. Please install LibreOffice.")
            return None
        except Exception as e:
            logger.error(f"Error converting {ppt_file.name}: {str(e)}")
            return None
    
    def detect_white_border(self, image):
        """
        Detect white borders in an image
        
        Args:
            image (PIL.Image): Input image
            
        Returns:
            tuple: (left, top, right, bottom) border sizes
        """
        # Convert to numpy array
        img_array = np.array(image)
        
        # Handle different image modes
        if len(img_array.shape) == 3:  # RGB/RGBA
            # Check if pixel is close to white (allowing for slight variations)
            white_threshold = 240
            is_white = np.all(img_array[:, :, :3] >= white_threshold, axis=2)
        else:  # Grayscale
            white_threshold = 240
            is_white = img_array >= white_threshold
        
        # Find borders
        height, width = is_white.shape
        
        # Top border
        top = 0
        for row in range(height):
            if not np.all(is_white[row, :]):
                break
            top += 1
        
        # Bottom border
        bottom = 0
        for row in range(height - 1, -1, -1):
            if not np.all(is_white[row, :]):
                break
            bottom += 1
        
        # Left border
        left = 0
        for col in range(width):
            if not np.all(is_white[:, col]):
                break
            left += 1
        
        # Right border
        right = 0
        for col in range(width - 1, -1, -1):
            if not np.all(is_white[:, col]):
                break
            right += 1
        
        return left, top, right, bottom
    
    def remove_white_border(self, image_path):
        """
        Remove excessive white borders from an image while preserving content aspect ratio
        
        Args:
            image_path (Path): Path to the image file
        """
        try:
            with Image.open(image_path) as img:
                original_size = img.size
                logger.info(f"Processing {image_path.name} - Original size: {original_size[0]}x{original_size[1]}")
                
                # Detect borders
                left, top, right, bottom = self.detect_white_border(img)
                
                # Only remove borders if they exceed the threshold, but keep minimum border
                crop_left = max(0, left - self.min_border) if left > self.border_threshold else 0
                crop_top = max(0, top - self.min_border) if top > self.border_threshold else 0
                crop_right = max(0, right - self.min_border) if right > self.border_threshold else 0
                crop_bottom = max(0, bottom - self.min_border) if bottom > self.border_threshold else 0
                
                # If any borders need to be removed
                if any([crop_left, crop_top, crop_right, crop_bottom]):
                    width, height = img.size
                    
                    # Calculate crop box
                    crop_box = (
                        crop_left,
                        crop_top,
                        width - crop_right,
                        height - crop_bottom
                    )
                    
                    # Ensure crop box is valid
                    if crop_box[2] > crop_box[0] and crop_box[3] > crop_box[1]:
                        # Crop the image
                        cropped_img = img.crop(crop_box)
                        
                        # Save the cropped image with high quality
                        cropped_img.save(image_path, "PNG", dpi=(self.dpi, self.dpi), optimize=False)
                        
                        new_size = cropped_img.size
                        logger.info(f"Removed borders from {image_path.name}: "
                                  f"L:{crop_left}, T:{crop_top}, R:{crop_right}, B:{crop_bottom}")
                        logger.info(f"New size: {new_size[0]}x{new_size[1]} (was {original_size[0]}x{original_size[1]})")
                    else:
                        logger.warning(f"Invalid crop box for {image_path.name}, skipping border removal")
                else:
                    logger.info(f"No significant borders found in {image_path.name}")
                    
        except Exception as e:
            logger.error(f"Error processing {image_path.name}: {str(e)}")
    
    def process_png_files(self, folder_path):
        """Process all PNG files in a folder to remove white borders"""
        png_files = list(folder_path.glob("*.png"))
        
        for png_file in png_files:
            self.remove_white_border(png_file)
    
    def convert_all(self):
        """Convert all PowerPoint files and process the resulting PNG files"""
        ppt_files = self.find_ppt_files()
        
        if not ppt_files:
            logger.warning(f"No PowerPoint files found in {self.input_folder}")
            return
        
        logger.info(f"Found {len(ppt_files)} PowerPoint files")
        
        for ppt_file in ppt_files:
            logger.info(f"Processing {ppt_file.name}...")
            
            # Try Windows COM automation first, then LibreOffice
            output_folder = self.convert_ppt_to_png_windows(ppt_file)
            if output_folder is None:
                output_folder = self.convert_ppt_to_png_libreoffice(ppt_file)
            
            if output_folder and output_folder.exists():
                # Process PNG files to remove white borders
                logger.info(f"Processing PNG files in {output_folder}")
                self.process_png_files(output_folder)
            else:
                logger.error(f"Failed to convert {ppt_file.name}")
        
        logger.info("Conversion completed!")


def main():
    """Main function to run the converter"""
    if len(sys.argv) < 2:
        print("Usage: python ppt_to_png_converter.py <input_folder> [output_folder] [dpi] [border_threshold] [min_border] [single_folder]")
        print("Example: python ppt_to_png_converter.py ./presentations ./output 300 50 10 true")
        print("Parameters:")
        print("  input_folder: Path to folder containing PPT files (required)")
        print("  output_folder: Output folder path (optional, default: input_folder/output)")
        print("  dpi: PNG resolution (optional, default: 300)")
        print("  border_threshold: Min border size to remove in pixels (optional, default: 50)")
        print("  min_border: Min white border to keep after cropping (optional, default: 10)")
        print("  single_folder: Save all PNGs in one folder - true/false (optional, default: false)")
        sys.exit(1)
    
    input_folder = sys.argv[1]
    output_folder = sys.argv[2] if len(sys.argv) > 2 else None
    dpi = int(sys.argv[3]) if len(sys.argv) > 3 else 300
    border_threshold = int(sys.argv[4]) if len(sys.argv) > 4 else 50
    min_border = int(sys.argv[5]) if len(sys.argv) > 5 else 10
    single_folder = sys.argv[6].lower() == 'true' if len(sys.argv) > 6 else False
    
    # Validate input folder
    if not os.path.exists(input_folder):
        print(f"Error: Input folder '{input_folder}' does not exist")
        sys.exit(1)
    
    # Create converter and run
    converter = PPTToPNGConverter(
        input_folder=input_folder,
        output_folder=output_folder,
        dpi=dpi,
        border_threshold=border_threshold,
        min_border=min_border,
        single_folder=single_folder
    )
    
    converter.convert_all()


if __name__ == "__main__":
    main()
