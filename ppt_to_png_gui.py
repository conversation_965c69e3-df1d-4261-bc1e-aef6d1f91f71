#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT to PNG Converter - GUI Version

A user-friendly graphical interface for converting PowerPoint files to PNG images
with white border removal and aspect ratio preservation.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import sys
import os
from pathlib import Path
import queue
import logging

# Import the converter class
from ppt_to_png_converter import PPTToPNGConverter

class PPTConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PPT to PNG Converter v2.0")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Variables
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.dpi = tk.IntVar(value=300)
        self.border_threshold = tk.IntVar(value=50)
        self.min_border = tk.IntVar(value=10)
        self.single_folder = tk.BooleanVar(value=False)
        
        # Queue for thread communication
        self.log_queue = queue.Queue()
        
        # Conversion state
        self.is_converting = False
        
        self.create_widgets()
        self.setup_logging()
        
        # Start log monitoring
        self.root.after(100, self.process_log_queue)
    
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="PPT to PNG Converter", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input folder selection
        ttk.Label(main_frame, text="输入文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.input_folder, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.select_input_folder).grid(row=1, column=2, padx=(5, 0))
        
        # Output folder selection
        ttk.Label(main_frame, text="输出文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_folder, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.select_output_folder).grid(row=2, column=2, padx=(5, 0))
        
        # Parameters frame
        params_frame = ttk.LabelFrame(main_frame, text="转换参数", padding="10")
        params_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        params_frame.columnconfigure(1, weight=1)
        params_frame.columnconfigure(3, weight=1)
        
        # DPI setting
        ttk.Label(params_frame, text="图像分辨率 (DPI):").grid(row=0, column=0, sticky=tk.W, pady=5)
        dpi_spinbox = ttk.Spinbox(params_frame, from_=150, to=1200, increment=50, 
                                 textvariable=self.dpi, width=10)
        dpi_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        # Border threshold
        ttk.Label(params_frame, text="白边阈值 (像素):").grid(row=0, column=2, sticky=tk.W, pady=5)
        threshold_spinbox = ttk.Spinbox(params_frame, from_=10, to=500, increment=10,
                                       textvariable=self.border_threshold, width=10)
        threshold_spinbox.grid(row=0, column=3, sticky=tk.W, padx=(5, 0))
        
        # Minimum border
        ttk.Label(params_frame, text="保留白边 (像素):").grid(row=1, column=0, sticky=tk.W, pady=5)
        min_border_spinbox = ttk.Spinbox(params_frame, from_=0, to=100, increment=5,
                                        textvariable=self.min_border, width=10)
        min_border_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(5, 20))
        
        # Single folder option
        single_folder_check = ttk.Checkbutton(params_frame, text="所有PNG保存在同一文件夹", 
                                            variable=self.single_folder)
        single_folder_check.grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=(5, 0))
        
        # Help text
        help_frame = ttk.LabelFrame(main_frame, text="参数说明", padding="10")
        help_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        help_text = """• 图像分辨率: 输出PNG的DPI，越高图像越清晰但文件越大 (推荐300-600)
• 白边阈值: 只有大于此值的白边才会被移除 (像素)
• 保留白边: 移除白边后保留的最小白边大小 (像素)
• 同一文件夹: 勾选后所有PNG以"PPT名_slide编号.png"格式保存在一个文件夹中"""
        
        ttk.Label(help_frame, text=help_text, justify=tk.LEFT, wraplength=750).grid(row=0, column=0, sticky=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                        command=self.start_conversion, style='Accent.TButton')
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止转换", 
                                     command=self.stop_conversion, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Log display
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="5")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E))
    
    def select_input_folder(self):
        """Select input folder containing PPT files"""
        folder = filedialog.askdirectory(title="选择包含PPT文件的文件夹")
        if folder:
            self.input_folder.set(folder)
            # Auto-set output folder if not set
            if not self.output_folder.get():
                self.output_folder.set(os.path.join(folder, "PNG输出"))
    
    def select_output_folder(self):
        """Select output folder for PNG files"""
        folder = filedialog.askdirectory(title="选择PNG输出文件夹")
        if folder:
            self.output_folder.set(folder)
    
    def setup_logging(self):
        """Setup logging to capture converter output"""
        # Create a custom handler that puts logs into the queue
        class QueueHandler(logging.Handler):
            def __init__(self, log_queue):
                super().__init__()
                self.log_queue = log_queue
            
            def emit(self, record):
                self.log_queue.put(self.format(record))
        
        # Configure logging
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Add queue handler
        queue_handler = QueueHandler(self.log_queue)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        queue_handler.setFormatter(formatter)
        logger.addHandler(queue_handler)
    
    def process_log_queue(self):
        """Process log messages from the queue and display them"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message + '\n')
                self.log_text.see(tk.END)
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.process_log_queue)
    
    def clear_log(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)
    
    def validate_inputs(self):
        """Validate user inputs"""
        if not self.input_folder.get():
            messagebox.showerror("错误", "请选择输入文件夹")
            return False
        
        if not os.path.exists(self.input_folder.get()):
            messagebox.showerror("错误", "输入文件夹不存在")
            return False
        
        if not self.output_folder.get():
            messagebox.showerror("错误", "请选择输出文件夹")
            return False
        
        return True
    
    def start_conversion(self):
        """Start the conversion process in a separate thread"""
        if not self.validate_inputs():
            return
        
        if self.is_converting:
            messagebox.showwarning("警告", "转换正在进行中")
            return
        
        # Update UI state
        self.is_converting = True
        self.convert_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()
        self.status_var.set("转换中...")
        
        # Start conversion in separate thread
        self.conversion_thread = threading.Thread(target=self.run_conversion, daemon=True)
        self.conversion_thread.start()
    
    def run_conversion(self):
        """Run the actual conversion process"""
        try:
            # Create converter instance
            converter = PPTToPNGConverter(
                input_folder=self.input_folder.get(),
                output_folder=self.output_folder.get(),
                dpi=self.dpi.get(),
                border_threshold=self.border_threshold.get(),
                min_border=self.min_border.get(),
                single_folder=self.single_folder.get()
            )
            
            # Run conversion
            converter.convert_all()
            
            # Update UI on completion
            self.root.after(0, self.conversion_completed, True, "转换完成！")
            
        except Exception as e:
            error_msg = f"转换过程中发生错误: {str(e)}"
            self.root.after(0, self.conversion_completed, False, error_msg)
    
    def conversion_completed(self, success, message):
        """Handle conversion completion"""
        self.is_converting = False
        self.convert_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress.stop()
        
        if success:
            self.status_var.set("转换完成")
            messagebox.showinfo("完成", message)
        else:
            self.status_var.set("转换失败")
            messagebox.showerror("错误", message)
    
    def stop_conversion(self):
        """Stop the conversion process (placeholder)"""
        # Note: Actual stopping would require more complex thread management
        messagebox.showinfo("提示", "转换停止功能需要在实际实现中添加")
        self.status_var.set("用户取消")


def main():
    """Main function to run the GUI"""
    # Create and configure root window
    root = tk.Tk()
    
    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')
    except:
        pass
    
    # Create GUI application
    app = PPTConverterGUI(root)
    
    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    # Start GUI event loop
    root.mainloop()


if __name__ == "__main__":
    main()
