@echo off
echo PPT to PNG Converter
echo =====================
echo.
echo Usage Examples:
echo   PPT_to_PNG_Converter.exe "input_folder"
echo   PPT_to_PNG_Converter.exe "input_folder" "output_folder" 600 100 10 true
echo.
echo Parameters:
echo   1. input_folder: Path to folder with PPT files (required)
echo   2. output_folder: Output folder (optional)
echo   3. dpi: Image resolution (optional, default: 300)
echo   4. border_threshold: Min border size to remove (optional, default: 50)
echo   5. min_border: Min border to keep (optional, default: 10)
echo   6. single_folder: Save all in one folder - true/false (optional, default: false)
echo.
pause
