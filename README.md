# PowerPoint to PNG Converter with White Border Removal

这个Python脚本可以将指定文件夹下的所有PowerPoint文件（.ppt, .pptx, .pptm）转换为高分辨率PNG图片，并自动检测和去除过大的白边。

## 功能特点

- 支持批量处理多个PPT文件
- 高分辨率PNG输出（默认300 DPI）
- 智能检测和去除过大的白边
- 支持Windows COM自动化和LibreOffice两种转换方式
- 详细的日志输出

## 安装依赖

```bash
pip install -r requirements.txt
```

### 依赖说明

- **Pillow**: 图像处理库，用于PNG文件的白边检测和裁剪
- **numpy**: 数值计算库，用于图像数组处理
- **comtypes**: Windows COM接口库，用于PowerPoint自动化（仅Windows）
- **python-pptx**: PowerPoint文件处理库（备用方案）

## 使用方法

### 基本用法

```bash
python ppt_to_png_converter.py <输入文件夹路径>
```

### 完整参数

```bash
python ppt_to_png_converter.py <输入文件夹> [输出文件夹] [DPI] [白边阈值]
```

### 参数说明

- `输入文件夹`: 包含PPT文件的文件夹路径（必需）
- `输出文件夹`: PNG文件输出路径（可选，默认为输入文件夹下的output子文件夹）
- `DPI`: PNG文件分辨率（可选，默认300）
- `白边阈值`: 最小白边像素数，超过此值才会被去除（可选，默认50像素）

### 使用示例

```bash
# 基本使用 - 处理当前文件夹下的PPT文件
python ppt_to_png_converter.py ./presentations

# 指定输出文件夹和高分辨率
python ppt_to_png_converter.py ./presentations ./output 600 30

# 使用相对路径
python ppt_to_png_converter.py "C:\Users\<USER>\Documents\PPT文件" "C:\Users\<USER>\Documents\输出"
```

## 工作原理

### PPT转换

1. **Windows方式**: 使用COM自动化调用本地安装的PowerPoint应用程序
2. **跨平台方式**: 使用LibreOffice headless模式进行转换

### 白边检测和去除

1. 将PNG图像转换为numpy数组
2. 检测图像四边的白色像素（RGB值≥240）
3. 计算每边连续白色像素的数量
4. 如果白边大小超过设定阈值，则进行裁剪
5. 保存处理后的图像

## 输出结构

```
输出文件夹/
├── PPT文件名1/
│   ├── slide_001.png
│   ├── slide_002.png
│   └── ...
├── PPT文件名2/
│   ├── slide_001.png
│   ├── slide_002.png
│   └── ...
└── ...
```

## 注意事项

### Windows用户

- 建议安装Microsoft PowerPoint以获得最佳转换效果
- 如果没有PowerPoint，脚本会自动尝试使用LibreOffice

### Linux/Mac用户

- 需要安装LibreOffice: `sudo apt-get install libreoffice` (Ubuntu) 或 `brew install libreoffice` (Mac)

### 性能优化

- 大文件处理可能需要较长时间
- 建议分批处理大量PPT文件
- 高DPI设置会增加文件大小和处理时间

## 故障排除

### 常见问题

1. **ImportError: No module named 'comtypes'**
   - 解决方案: `pip install comtypes`

2. **LibreOffice not found**
   - 解决方案: 安装LibreOffice并确保在系统PATH中

3. **PowerPoint COM错误**
   - 解决方案: 确保PowerPoint已正确安装且未被其他程序占用

4. **内存不足**
   - 解决方案: 降低DPI设置或分批处理文件

### 日志信息

脚本会输出详细的处理日志，包括：
- 找到的PPT文件数量
- 每个幻灯片的转换状态
- 白边检测和去除结果
- 错误信息和警告

## 自定义配置

可以通过修改脚本中的以下参数来调整行为：

```python
# 白色像素阈值（RGB值）
white_threshold = 240

# 支持的文件扩展名
ppt_extensions = {'.ppt', '.pptx', '.pptm'}

# 默认输出比例
aspect_ratio = (8, 6)  # 宽:高
```
