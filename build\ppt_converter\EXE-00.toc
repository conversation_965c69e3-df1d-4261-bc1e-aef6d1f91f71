('C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)\\dist\\PPT_to_PNG_Converter.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
 '(26)\\build\\ppt_converter\\PPT_to_PNG_Converter.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)\\build\\ppt_converter\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('ppt_to_png_converter',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)\\ppt_to_png_converter.py',
   'PYSOURCE'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_def.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_def.2.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('msvcp140_1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('mkl_pgi_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_pgi_thread.2.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_intelmpi_ilp64.2.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('tbb12.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbb12.dll',
   'BINARY'),
  ('tbbmalloc.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbbmalloc.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('bzip2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\bzip2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi-7.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi-7.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_mc3.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_mc3.2.dll',
   'BINARY'),
  ('mkl_vml_avx.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx.2.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx512.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx512.2.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_intelmpi_lp64.2.dll',
   'BINARY'),
  ('mkl_sequential.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_sequential.2.dll',
   'BINARY'),
  ('mkl_def.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_def.2.dll',
   'BINARY'),
  ('mkl_core.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_core.2.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('mkl_msg.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_msg.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('mkl_mc.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_mc.2.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('libiomp5md.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libiomp5md.dll',
   'BINARY'),
  ('vccorlib140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vccorlib140.dll',
   'BINARY'),
  ('omptarget.rtl.opencl.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.rtl.opencl.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('mkl_scalapack_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_scalapack_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libiompstubs5md.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libiompstubs5md.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('msvcp140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140.dll',
   'BINARY'),
  ('msvcp140_2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('msvcp140_codecvt_ids.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_codecvt_ids.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx2.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx2.2.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_msmpi_ilp64.2.dll',
   'BINARY'),
  ('libbz2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('omptarget.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.dll',
   'BINARY'),
  ('mkl_scalapack_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_scalapack_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('ffi.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('mkl_avx.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx.2.dll',
   'BINARY'),
  ('mkl_vml_cmpt.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_cmpt.2.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('mkl_intel_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_intel_thread.2.dll',
   'BINARY'),
  ('omptarget.sycl.wrap.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.sycl.wrap.dll',
   'BINARY'),
  ('mkl_tbb_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_tbb_thread.2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ucrtbase.dll',
   'BINARY'),
  ('ffi-8.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('omptarget.rtl.level0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.rtl.level0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('vcruntime140_1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('mkl_mc3.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_mc3.2.dll',
   'BINARY'),
  ('tbbmalloc_proxy.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbbmalloc_proxy.dll',
   'BINARY'),
  ('mkl_blacs_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_ilp64.2.dll',
   'BINARY'),
  ('libimalloc.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libimalloc.dll',
   'BINARY'),
  ('mkl_cdft_core.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_cdft_core.2.dll',
   'BINARY'),
  ('concrt140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\concrt140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('vcruntime140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcruntime140.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_msmpi_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\zlib.dll', 'BINARY'),
  ('mkl_avx2.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx2.2.dll',
   'BINARY'),
  ('mkl_vml_mc.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_mc.2.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('vcomp140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcomp140.dll',
   'BINARY'),
  ('msvcp140_atomic_wait.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_atomic_wait.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('mkl_rt.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_rt.2.dll',
   'BINARY'),
  ('mkl_avx512.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx512.2.dll',
   'BINARY'),
  ('python311.dll', 'C:\\Users\\<USER>\\anaconda3\\python311.dll', 'BINARY'),
  ('_lzma.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mkl\\_py_mkl_service.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\mkl\\_py_mkl_service.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mkl\\_mklinit.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\mkl\\_mklinit.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('yaml.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\yaml.dll', 'BINARY'),
  ('libwebpmux.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebpmux.dll',
   'BINARY'),
  ('libwebp.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebp.dll',
   'BINARY'),
  ('libwebpdemux.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebpdemux.dll',
   'BINARY'),
  ('lcms2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\lcms2.dll',
   'BINARY'),
  ('tiff.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tiff.dll', 'BINARY'),
  ('openjp2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\openjp2.dll',
   'BINARY'),
  ('libsharpyuv.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libsharpyuv.dll',
   'BINARY'),
  ('zstd.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\zstd.dll', 'BINARY'),
  ('Lerc.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\Lerc.dll', 'BINARY'),
  ('deflate.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\deflate.dll',
   'BINARY'),
  ('importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1753001364,
 [('run.exe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\anaconda3\\python311.dll')
