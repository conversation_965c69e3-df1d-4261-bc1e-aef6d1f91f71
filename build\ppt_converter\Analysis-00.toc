(['C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)\\ppt_to_png_converter.py'],
 ['C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)'],
 ['comtypes.gen',
  'comtypes.client',
  'PIL',
  'PIL.Image',
  'PIL.ImageChops',
  'numpy',
  'pathlib',
  'logging'],
 [('C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\rapidfuzz\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter', 'matplotlib', 'scipy', 'pandas', 'jupyter', 'IPython', '__main__'],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.5 | packaged by Anaconda, Inc. | (main, Sep 11 2023, 13:26:23) [MSC '
 'v.1916 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('ppt_to_png_converter',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 (26)\\ppt_to_png_converter.py',
   'PYSOURCE')],
 [('pkg_resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('struct', 'C:\\Users\\<USER>\\anaconda3\\Lib\\struct.py', 'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('__future__', 'C:\\Users\\<USER>\\anaconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'C:\\Users\\<USER>\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Users\\<USER>\\anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'C:\\Users\\<USER>\\anaconda3\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'C:\\Users\\<USER>\\anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Users\\<USER>\\anaconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Users\\<USER>\\anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Users\\<USER>\\anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform', 'C:\\Users\\<USER>\\anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\anaconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'C:\\Users\\<USER>\\anaconda3\\Lib\\cgi.py', 'PYMODULE'),
  ('html', 'C:\\Users\\<USER>\\anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'C:\\Users\\<USER>\\anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'C:\\Users\\<USER>\\anaconda3\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Users\\<USER>\\anaconda3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Users\\<USER>\\anaconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Users\\<USER>\\anaconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('comtypes.gen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\gen\\__init__.py',
   'PYMODULE'),
  ('comtypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\__init__.py',
   'PYMODULE'),
  ('comtypes._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_meta.py',
   'PYMODULE'),
  ('comtypes._comobject',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_comobject.py',
   'PYMODULE'),
  ('comtypes.errorinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\errorinfo.py',
   'PYMODULE'),
  ('comtypes.automation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\automation.py',
   'PYMODULE'),
  ('comtypes.client.dynamic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\dynamic.py',
   'PYMODULE'),
  ('comtypes.client.lazybind',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\lazybind.py',
   'PYMODULE'),
  ('comtypes.safearray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\safearray.py',
   'PYMODULE'),
  ('comtypes._vtbl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_vtbl.py',
   'PYMODULE'),
  ('comtypes._post_coinit.instancemethod',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\instancemethod.py',
   'PYMODULE'),
  ('comtypes._post_coinit.misc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\misc.py',
   'PYMODULE'),
  ('comtypes.server',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\server\\__init__.py',
   'PYMODULE'),
  ('comtypes._post_coinit.bstr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\bstr.py',
   'PYMODULE'),
  ('comtypes._post_coinit.unknwn',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\unknwn.py',
   'PYMODULE'),
  ('comtypes._post_coinit._cominterface_meta_patcher',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\_cominterface_meta_patcher.py',
   'PYMODULE'),
  ('comtypes._post_coinit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_post_coinit\\__init__.py',
   'PYMODULE'),
  ('comtypes._memberspec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_memberspec.py',
   'PYMODULE'),
  ('comtypes._tlib_version_checker',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_tlib_version_checker.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\__init__.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.codegenerator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\codegenerator.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\helpers.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.comments',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\comments.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\typedesc.py',
   'PYMODULE'),
  ('comtypes.tools.typedesc_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\typedesc_base.py',
   'PYMODULE'),
  ('comtypes.tools.tlbparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\tlbparser.py',
   'PYMODULE'),
  ('comtypes.persist',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\persist.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.packing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\packing.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.namespaces',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\namespaces.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.heads',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\heads.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.typeannotator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\typeannotator.py',
   'PYMODULE'),
  ('comtypes.tools.codegenerator.modulenamer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\codegenerator\\modulenamer.py',
   'PYMODULE'),
  ('comtypes.tools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\tools\\__init__.py',
   'PYMODULE'),
  ('comtypes.typeinfo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\typeinfo.py',
   'PYMODULE'),
  ('comtypes.messageloop',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\messageloop.py',
   'PYMODULE'),
  ('comtypes.hresult',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\hresult.py',
   'PYMODULE'),
  ('comtypes._safearray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_safearray.py',
   'PYMODULE'),
  ('comtypes.GUID',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\GUID.py',
   'PYMODULE'),
  ('comtypes._npsupport',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\_npsupport.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('threadpoolctl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'C:\\Users\\<USER>\\anaconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'C:\\Users\\<USER>\\anaconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Users\\<USER>\\anaconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Users\\<USER>\\anaconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('comtypes.patcher',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\patcher.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('comtypes.client',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\__init__.py',
   'PYMODULE'),
  ('comtypes.stream',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\stream.py',
   'PYMODULE'),
  ('comtypes.client._create',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_create.py',
   'PYMODULE'),
  ('comtypes.client._activeobj',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_activeobj.py',
   'PYMODULE'),
  ('comtypes.client._managing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_managing.py',
   'PYMODULE'),
  ('comtypes.client._generate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_generate.py',
   'PYMODULE'),
  ('comtypes.client._events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_events.py',
   'PYMODULE'),
  ('comtypes.connectionpoints',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\connectionpoints.py',
   'PYMODULE'),
  ('comtypes.client._constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_constants.py',
   'PYMODULE'),
  ('comtypes.client._code_cache',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\comtypes\\client\\_code_cache.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('mkl',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\mkl\\__init__.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Users\\<USER>\\anaconda3\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('defusedxml',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pathlib.py', 'PYMODULE')],
 [('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_def.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_def.2.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('msvcp140_1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('mkl_pgi_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_pgi_thread.2.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_intelmpi_ilp64.2.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('tbb12.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbb12.dll',
   'BINARY'),
  ('tbbmalloc.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbbmalloc.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('bzip2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\bzip2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi-7.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi-7.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_mc3.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_mc3.2.dll',
   'BINARY'),
  ('mkl_vml_avx.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx.2.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx512.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx512.2.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_intelmpi_lp64.2.dll',
   'BINARY'),
  ('mkl_sequential.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_sequential.2.dll',
   'BINARY'),
  ('mkl_def.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_def.2.dll',
   'BINARY'),
  ('mkl_core.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_core.2.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('mkl_msg.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_msg.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('mkl_mc.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_mc.2.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('libiomp5md.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libiomp5md.dll',
   'BINARY'),
  ('vccorlib140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vccorlib140.dll',
   'BINARY'),
  ('omptarget.rtl.opencl.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.rtl.opencl.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('mkl_scalapack_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_scalapack_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libiompstubs5md.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libiompstubs5md.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('msvcp140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140.dll',
   'BINARY'),
  ('msvcp140_2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('msvcp140_codecvt_ids.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_codecvt_ids.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx2.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_avx2.2.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_msmpi_ilp64.2.dll',
   'BINARY'),
  ('libbz2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('omptarget.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.dll',
   'BINARY'),
  ('mkl_scalapack_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_scalapack_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('ffi.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('mkl_avx.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx.2.dll',
   'BINARY'),
  ('mkl_vml_cmpt.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_cmpt.2.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('mkl_intel_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_intel_thread.2.dll',
   'BINARY'),
  ('omptarget.sycl.wrap.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.sycl.wrap.dll',
   'BINARY'),
  ('mkl_tbb_thread.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_tbb_thread.2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ucrtbase.dll',
   'BINARY'),
  ('ffi-8.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('omptarget.rtl.level0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\omptarget.rtl.level0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('vcruntime140_1.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('mkl_mc3.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_mc3.2.dll',
   'BINARY'),
  ('tbbmalloc_proxy.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tbbmalloc_proxy.dll',
   'BINARY'),
  ('mkl_blacs_ilp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_ilp64.2.dll',
   'BINARY'),
  ('libimalloc.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libimalloc.dll',
   'BINARY'),
  ('mkl_cdft_core.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_cdft_core.2.dll',
   'BINARY'),
  ('concrt140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\concrt140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('vcruntime140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcruntime140.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_lp64.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_blacs_msmpi_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\zlib.dll', 'BINARY'),
  ('mkl_avx2.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx2.2.dll',
   'BINARY'),
  ('mkl_vml_mc.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_vml_mc.2.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('vcomp140.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\vcomp140.dll',
   'BINARY'),
  ('msvcp140_atomic_wait.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\msvcp140_atomic_wait.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('mkl_rt.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_rt.2.dll',
   'BINARY'),
  ('mkl_avx512.2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\mkl_avx512.2.dll',
   'BINARY'),
  ('python311.dll', 'C:\\Users\\<USER>\\anaconda3\\python311.dll', 'BINARY'),
  ('_lzma.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\psutil\\_psutil_windows.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mkl\\_py_mkl_service.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\mkl\\_py_mkl_service.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mkl\\_mklinit.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\mkl\\_mklinit.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('yaml.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\yaml.dll', 'BINARY'),
  ('libwebpmux.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebpmux.dll',
   'BINARY'),
  ('libwebp.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebp.dll',
   'BINARY'),
  ('libwebpdemux.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libwebpdemux.dll',
   'BINARY'),
  ('lcms2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\lcms2.dll',
   'BINARY'),
  ('tiff.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\tiff.dll', 'BINARY'),
  ('openjp2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\openjp2.dll',
   'BINARY'),
  ('libsharpyuv.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libsharpyuv.dll',
   'BINARY'),
  ('zstd.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\zstd.dll', 'BINARY'),
  ('Lerc.dll', 'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\Lerc.dll', 'BINARY'),
  ('deflate.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\deflate.dll',
   'BINARY')],
 [],
 [],
 [('importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\requires.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\SOURCES.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\not-zip-safe',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\dependency_links.txt',
   'DATA'),
  ('wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\wheel-0.38.4-py3.11.egg-info\\PKG-INFO',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\新建文件夹 '
   '(26)\\build\\ppt_converter\\base_library.zip',
   'DATA')],
 [('warnings', 'C:\\Users\\<USER>\\anaconda3\\Lib\\warnings.py', 'PYMODULE'),
  ('enum', 'C:\\Users\\<USER>\\anaconda3\\Lib\\enum.py', 'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('types', 'C:\\Users\\<USER>\\anaconda3\\Lib\\types.py', 'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('linecache', 'C:\\Users\\<USER>\\anaconda3\\Lib\\linecache.py', 'PYMODULE'),
  ('functools', 'C:\\Users\\<USER>\\anaconda3\\Lib\\functools.py', 'PYMODULE'),
  ('ntpath', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ntpath.py', 'PYMODULE'),
  ('codecs', 'C:\\Users\\<USER>\\anaconda3\\Lib\\codecs.py', 'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('stat', 'C:\\Users\\<USER>\\anaconda3\\Lib\\stat.py', 'PYMODULE'),
  ('operator', 'C:\\Users\\<USER>\\anaconda3\\Lib\\operator.py', 'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\genericpath.py',
   'PYMODULE'),
  ('sre_parse', 'C:\\Users\\<USER>\\anaconda3\\Lib\\sre_parse.py', 'PYMODULE'),
  ('heapq', 'C:\\Users\\<USER>\\anaconda3\\Lib\\heapq.py', 'PYMODULE'),
  ('locale', 'C:\\Users\\<USER>\\anaconda3\\Lib\\locale.py', 'PYMODULE'),
  ('keyword', 'C:\\Users\\<USER>\\anaconda3\\Lib\\keyword.py', 'PYMODULE'),
  ('io', 'C:\\Users\\<USER>\\anaconda3\\Lib\\io.py', 'PYMODULE'),
  ('posixpath', 'C:\\Users\\<USER>\\anaconda3\\Lib\\posixpath.py', 'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'C:\\Users\\<USER>\\anaconda3\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('reprlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\reprlib.py', 'PYMODULE'),
  ('weakref', 'C:\\Users\\<USER>\\anaconda3\\Lib\\weakref.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('traceback', 'C:\\Users\\<USER>\\anaconda3\\Lib\\traceback.py', 'PYMODULE'),
  ('abc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\abc.py', 'PYMODULE'),
  ('copyreg', 'C:\\Users\\<USER>\\anaconda3\\Lib\\copyreg.py', 'PYMODULE'),
  ('os', 'C:\\Users\\<USER>\\anaconda3\\Lib\\os.py', 'PYMODULE')])
